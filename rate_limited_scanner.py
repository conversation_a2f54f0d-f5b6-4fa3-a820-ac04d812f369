"""
Rate-Limited Scanner for Paper Trading
Optimized to work within FMP API rate limits
"""

import time
import asyncio
from datetime import datetime
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed

from config import config
from logger import log_info, log_debug, log_error, log_warning
from data_provider import data_provider
from scanner import scanner
from simple_ai import simple_ai
from technical_analysis import TechnicalAnalyzer

class RateLimitedScanner:
    """Scanner optimized for API rate limits"""

    def __init__(self):
        self.last_scan_time = 0
        self.scan_count = 0
        self.successful_scans = 0
        self.failed_scans = 0
        self.technical_analyzer = TechnicalAnalyzer()
        
        # Smaller, focused symbol list to avoid rate limits
        self.focus_symbols = [
            'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA',
            'NVDA', 'META', 'NFLX', 'AMD', 'CRM',
            'UBER', 'SHOP', 'SQ', 'ROKU', 'ZM',
            'PLTR', 'COIN', 'RBLX', 'SNOW', 'DDOG'
        ]
        
    def run_focused_scan(self) -> List:
        """Run a focused scan on high-probability symbols"""
        try:
            scan_start = time.time()
            self.scan_count += 1
            
            log_info(f"Starting focused scan #{self.scan_count} with {len(self.focus_symbols)} symbols")
            
            signals = []
            successful_requests = 0
            failed_requests = 0
            
            # Process symbols with rate limiting
            for i, symbol in enumerate(self.focus_symbols):
                try:
                    # Add delay between requests
                    if i > 0:
                        time.sleep(config.system.api_request_delay)
                    
                    # Get market data
                    market_data = data_provider.get_market_data(symbol)
                    quote_data = market_data.__dict__ if market_data else None
                    
                    if quote_data:
                        successful_requests += 1
                        
                        # Use technical analysis to detect signals
                        try:
                            analysis = self.technical_analyzer.analyze_symbol(symbol, quote_data)
                            if analysis and analysis.get('signal_strength', 0) > 0.7:
                                # Create a simple signal object
                                from models import Signal, SignalType
                                signal = Signal(
                                    symbol=symbol,
                                    signal_type=SignalType.BUY if analysis.get('recommendation') == 'BUY' else SignalType.SELL,
                                    confidence=analysis.get('signal_strength', 0.7),
                                    entry_price=quote_data.get('price', 0),
                                    expected_profit=config.trading.target_profit_dollars,
                                    timestamp=datetime.now()
                                )
                                signals.append(signal)
                                log_debug(f"Found signal for {symbol}: {analysis.get('recommendation')} ({analysis.get('signal_strength', 0):.0%})")
                        except Exception as e:
                            log_debug(f"Analysis error for {symbol}: {e}")
                    else:
                        failed_requests += 1
                        log_warning(f"No data for {symbol}")
                        
                except Exception as e:
                    failed_requests += 1
                    log_error(f"Error scanning {symbol}: {e}")
            
            # Process signals through Simple AI
            if signals:
                try:
                    ai_signals = simple_ai.process_signals(signals)
                    
                    # Filter by AI criteria
                    filtered_signals = []
                    for ai_signal in ai_signals:
                        should_trade, reason = simple_ai.should_trade_signal(ai_signal)
                        if should_trade:
                            # Convert back to regular signal
                            enhanced_signal = next(s for s in signals if s.symbol == ai_signal.symbol)
                            enhanced_signal.confidence = ai_signal.enhanced_confidence
                            enhanced_signal.ai_enhanced = True
                            filtered_signals.append(enhanced_signal)
                    
                    signals = filtered_signals
                    
                except Exception as e:
                    log_error(f"AI processing error: {e}")
            
            scan_duration = time.time() - scan_start
            
            if successful_requests > failed_requests:
                self.successful_scans += 1
                log_info(f"Scan #{self.scan_count} completed: {len(signals)} signals, "
                        f"{successful_requests} successful requests, "
                        f"{failed_requests} failed requests, "
                        f"{scan_duration:.1f}s duration")
            else:
                self.failed_scans += 1
                log_warning(f"Scan #{self.scan_count} mostly failed: "
                           f"{successful_requests} successful, {failed_requests} failed")
            
            return signals
            
        except Exception as e:
            self.failed_scans += 1
            log_error(f"Scan #{self.scan_count} failed: {e}")
            return []
    
    def run_continuous_scan(self):
        """Run continuous scanning with rate limiting"""
        log_info("Starting rate-limited continuous scanning...")
        log_info(f"Scan interval: {config.system.scan_interval_seconds} seconds")
        log_info(f"API delay: {config.system.api_request_delay} seconds")
        log_info(f"Focus symbols: {len(self.focus_symbols)}")
        
        while True:
            try:
                current_time = time.time()
                
                # Check if enough time has passed since last scan
                time_since_last = current_time - self.last_scan_time
                if time_since_last < config.system.scan_interval_seconds:
                    sleep_time = config.system.scan_interval_seconds - time_since_last
                    log_debug(f"Waiting {sleep_time:.1f}s before next scan...")
                    time.sleep(sleep_time)
                
                # Run the scan
                signals = self.run_focused_scan()
                self.last_scan_time = time.time()
                
                # Display results
                if signals:
                    log_info(f"🎯 Found {len(signals)} trading signals:")
                    for i, signal in enumerate(signals[:5]):  # Show top 5
                        ai_status = "🤖 AI" if hasattr(signal, 'ai_enhanced') else "📊"
                        log_info(f"  {i+1}. {signal.symbol}: {signal.signal_type.value} "
                                f"({signal.confidence:.0%} confidence) {ai_status}")
                else:
                    log_info("No signals found in this scan")
                
                # Show scan statistics
                if self.scan_count % 5 == 0:  # Every 5 scans
                    success_rate = (self.successful_scans / self.scan_count * 100) if self.scan_count > 0 else 0
                    log_info(f"📊 Scan Stats: {self.scan_count} total, "
                            f"{self.successful_scans} successful ({success_rate:.1f}%)")
                
            except KeyboardInterrupt:
                log_info("Scanning stopped by user")
                break
            except Exception as e:
                log_error(f"Error in continuous scan: {e}")
                time.sleep(30)  # Wait before retrying
    
    def get_scan_summary(self) -> Dict[str, Any]:
        """Get scanning summary"""
        success_rate = (self.successful_scans / self.scan_count * 100) if self.scan_count > 0 else 0
        
        return {
            'total_scans': self.scan_count,
            'successful_scans': self.successful_scans,
            'failed_scans': self.failed_scans,
            'success_rate': success_rate,
            'focus_symbols': len(self.focus_symbols),
            'scan_interval': config.system.scan_interval_seconds,
            'api_delay': config.system.api_request_delay
        }

# Global scanner instance
rate_limited_scanner = RateLimitedScanner()

if __name__ == "__main__":
    print("🧪 RATE-LIMITED PAPER TRADING SCANNER")
    print("=" * 50)
    print("Optimized for FMP API rate limits")
    print(f"Focus symbols: {len(rate_limited_scanner.focus_symbols)}")
    print(f"Scan interval: {config.system.scan_interval_seconds} seconds")
    print(f"API delay: {config.system.api_request_delay} seconds")
    print("=" * 50)
    
    try:
        # Run a test scan
        print("\n🔍 Running test scan...")
        signals = rate_limited_scanner.run_focused_scan()
        print(f"✅ Test scan complete: {len(signals)} signals found")
        
        # Show summary
        summary = rate_limited_scanner.get_scan_summary()
        print(f"\n📊 Summary: {summary}")
        
        print("\n🚀 Ready for continuous scanning!")
        print("Use: python rate_limited_scanner.py")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        print("Check API configuration and rate limits")
