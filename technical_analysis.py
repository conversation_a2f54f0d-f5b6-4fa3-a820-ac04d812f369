"""
Technical analysis module for the MassiveScan trading bot.
Implements various technical indicators and signal generation logic.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import ta

from models import TechnicalIndicators, TradingSignal, SignalType, MarketData
from config import config
from logger import log_debug, log_info, log_warning

class TechnicalAnalyzer:
    """Technical analysis engine for generating trading signals"""
    
    def __init__(self):
        self.min_data_points = 50  # Minimum data points for reliable indicators
        
    def calculate_indicators(self, df: pd.DataFrame) -> TechnicalIndicators:
        """Calculate technical indicators from price data"""
        if len(df) < self.min_data_points:
            log_warning(f"Insufficient data points: {len(df)} < {self.min_data_points}")
            return None
        
        try:
            # Ensure required columns exist
            required_cols = ['open', 'high', 'low', 'close', 'volume']
            for col in required_cols:
                if col not in df.columns:
                    log_warning(f"Missing required column: {col}")
                    return None

            # Calculate RSI
            rsi = ta.momentum.RSIIndicator(df['close'], window=14).rsi()

            # Calculate MACD
            macd_indicator = ta.trend.MACD(df['close'])
            macd = macd_indicator.macd()
            macd_signal = macd_indicator.macd_signal()

            # Calculate Bollinger Bands
            bb_indicator = ta.volatility.BollingerBands(df['close'], window=20)
            bb_upper = bb_indicator.bollinger_hband()
            bb_middle = bb_indicator.bollinger_mavg()
            bb_lower = bb_indicator.bollinger_lband()

            # Calculate Moving Averages
            sma_20 = ta.trend.SMAIndicator(df['close'], window=20).sma_indicator()
            sma_50 = ta.trend.SMAIndicator(df['close'], window=50).sma_indicator()
            ema_12 = ta.trend.EMAIndicator(df['close'], window=12).ema_indicator()
            ema_26 = ta.trend.EMAIndicator(df['close'], window=26).ema_indicator()

            # Calculate Volume SMA
            volume_sma = ta.trend.SMAIndicator(df['volume'], window=20).sma_indicator()

            # Calculate ATR (Average True Range)
            atr = ta.volatility.AverageTrueRange(df['high'], df['low'], df['close'], window=14).average_true_range()
            
            # Get latest values
            latest_idx = -1
            
            indicators = TechnicalIndicators(
                symbol=df.get('symbol', [''])[0] if 'symbol' in df.columns else '',
                timestamp=datetime.now(),
                rsi=float(rsi.iloc[latest_idx]) if rsi is not None and not pd.isna(rsi.iloc[latest_idx]) else None,
                macd=float(macd.iloc[latest_idx]) if macd is not None and not pd.isna(macd.iloc[latest_idx]) else None,
                macd_signal=float(macd_signal.iloc[latest_idx]) if macd_signal is not None and not pd.isna(macd_signal.iloc[latest_idx]) else None,
                bb_upper=float(bb_upper.iloc[latest_idx]) if bb_upper is not None and not pd.isna(bb_upper.iloc[latest_idx]) else None,
                bb_middle=float(bb_middle.iloc[latest_idx]) if bb_middle is not None and not pd.isna(bb_middle.iloc[latest_idx]) else None,
                bb_lower=float(bb_lower.iloc[latest_idx]) if bb_lower is not None and not pd.isna(bb_lower.iloc[latest_idx]) else None,
                sma_20=float(sma_20.iloc[latest_idx]) if sma_20 is not None and not pd.isna(sma_20.iloc[latest_idx]) else None,
                sma_50=float(sma_50.iloc[latest_idx]) if sma_50 is not None and not pd.isna(sma_50.iloc[latest_idx]) else None,
                ema_12=float(ema_12.iloc[latest_idx]) if ema_12 is not None and not pd.isna(ema_12.iloc[latest_idx]) else None,
                ema_26=float(ema_26.iloc[latest_idx]) if ema_26 is not None and not pd.isna(ema_26.iloc[latest_idx]) else None,
                volume_sma=float(volume_sma.iloc[latest_idx]) if volume_sma is not None and not pd.isna(volume_sma.iloc[latest_idx]) else None,
                atr=float(atr.iloc[latest_idx]) if atr is not None and not pd.isna(atr.iloc[latest_idx]) else None
            )
            
            return indicators
            
        except Exception as e:
            log_warning(f"Error calculating indicators: {e}")
            return None
    
    def detect_momentum_breakout(self, df: pd.DataFrame, market_data: MarketData) -> Optional[TradingSignal]:
        """Detect momentum breakout signals"""
        if len(df) < 20:
            return None
        
        try:
            # Calculate price change and volume ratio
            current_price = market_data.price
            prev_close = df['close'].iloc[-2]
            price_change_pct = (current_price - prev_close) / prev_close
            
            volume_ratio = market_data.volume / market_data.avg_volume if market_data.avg_volume > 0 else 1
            
            # Check for momentum breakout conditions
            momentum_threshold = config.trading.momentum_threshold
            volume_threshold = config.trading.volume_surge_multiplier
            
            if (abs(price_change_pct) >= momentum_threshold and 
                volume_ratio >= volume_threshold):
                
                # Determine direction
                is_bullish = price_change_pct > 0
                
                # Calculate targets
                atr = self._calculate_atr(df)
                if not atr:
                    return None
                
                if is_bullish:
                    target_price = current_price + config.trading.target_profit_dollars / 100  # Rough estimate
                    stop_loss_price = current_price - config.trading.stop_loss_dollars / 100
                else:
                    target_price = current_price - config.trading.target_profit_dollars / 100
                    stop_loss_price = current_price + config.trading.stop_loss_dollars / 100
                
                # Calculate confidence based on momentum and volume
                confidence = min(0.95, 0.5 + (abs(price_change_pct) * 10) + (volume_ratio * 0.1))
                
                signal = TradingSignal(
                    symbol=market_data.symbol,
                    signal_type=SignalType.MOMENTUM_BREAKOUT,
                    timestamp=datetime.now(),
                    confidence=confidence,
                    entry_price=current_price,
                    target_price=target_price,
                    stop_loss_price=stop_loss_price,
                    expected_profit=config.trading.target_profit_dollars,
                    risk_reward_ratio=abs(target_price - current_price) / abs(current_price - stop_loss_price),
                    volume_ratio=volume_ratio,
                    technical_data={
                        'price_change_pct': price_change_pct,
                        'volume_ratio': volume_ratio,
                        'atr': atr,
                        'is_bullish': is_bullish
                    }
                )
                
                return signal
                
        except Exception as e:
            log_warning(f"Error detecting momentum breakout: {e}")
        
        return None
    
    def detect_rsi_signals(self, df: pd.DataFrame, market_data: MarketData) -> Optional[TradingSignal]:
        """Detect RSI oversold/overbought signals"""
        indicators = self.calculate_indicators(df)
        if not indicators or indicators.rsi is None:
            return None
        
        try:
            current_price = market_data.price
            rsi = indicators.rsi
            
            signal_type = None
            is_bullish = False
            
            # Check for oversold condition (potential buy signal)
            if rsi <= config.trading.rsi_oversold:
                signal_type = SignalType.RSI_OVERSOLD
                is_bullish = True
            
            # Check for overbought condition (potential sell signal)
            elif rsi >= config.trading.rsi_overbought:
                signal_type = SignalType.RSI_OVERBOUGHT
                is_bullish = False
            
            if signal_type:
                # Calculate targets
                atr = indicators.atr or (current_price * 0.02)  # Fallback to 2% of price
                
                if is_bullish:
                    target_price = current_price + config.trading.target_profit_dollars / 100
                    stop_loss_price = current_price - config.trading.stop_loss_dollars / 100
                else:
                    target_price = current_price - config.trading.target_profit_dollars / 100
                    stop_loss_price = current_price + config.trading.stop_loss_dollars / 100
                
                # Calculate confidence based on RSI extremity
                if is_bullish:
                    confidence = (config.trading.rsi_oversold - rsi) / config.trading.rsi_oversold
                else:
                    confidence = (rsi - config.trading.rsi_overbought) / (100 - config.trading.rsi_overbought)
                
                confidence = max(0.1, min(0.9, confidence))
                
                signal = TradingSignal(
                    symbol=market_data.symbol,
                    signal_type=signal_type,
                    timestamp=datetime.now(),
                    confidence=confidence,
                    entry_price=current_price,
                    target_price=target_price,
                    stop_loss_price=stop_loss_price,
                    expected_profit=config.trading.target_profit_dollars,
                    risk_reward_ratio=abs(target_price - current_price) / abs(current_price - stop_loss_price),
                    technical_data={
                        'rsi': rsi,
                        'atr': atr,
                        'is_bullish': is_bullish
                    }
                )
                
                return signal
                
        except Exception as e:
            log_warning(f"Error detecting RSI signals: {e}")
        
        return None
    
    def detect_volume_surge(self, df: pd.DataFrame, market_data: MarketData) -> Optional[TradingSignal]:
        """Detect volume surge with price movement"""
        if len(df) < 20:
            return None
        
        try:
            current_volume = market_data.volume
            avg_volume = market_data.avg_volume or df['volume'].tail(20).mean()
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1
            
            # Check for significant volume surge
            if volume_ratio >= config.trading.volume_surge_multiplier:
                current_price = market_data.price
                prev_close = df['close'].iloc[-1]
                price_change_pct = (current_price - prev_close) / prev_close
                
                # Only signal if there's also price movement
                if abs(price_change_pct) >= 0.005:  # 0.5% minimum price movement
                    is_bullish = price_change_pct > 0
                    
                    # Calculate targets
                    if is_bullish:
                        target_price = current_price + config.trading.target_profit_dollars / 100
                        stop_loss_price = current_price - config.trading.stop_loss_dollars / 100
                    else:
                        target_price = current_price - config.trading.target_profit_dollars / 100
                        stop_loss_price = current_price + config.trading.stop_loss_dollars / 100
                    
                    # Confidence based on volume ratio and price movement
                    confidence = min(0.9, 0.3 + (volume_ratio * 0.1) + (abs(price_change_pct) * 20))
                    
                    signal = TradingSignal(
                        symbol=market_data.symbol,
                        signal_type=SignalType.VOLUME_SURGE,
                        timestamp=datetime.now(),
                        confidence=confidence,
                        entry_price=current_price,
                        target_price=target_price,
                        stop_loss_price=stop_loss_price,
                        expected_profit=config.trading.target_profit_dollars,
                        risk_reward_ratio=abs(target_price - current_price) / abs(current_price - stop_loss_price),
                        volume_ratio=volume_ratio,
                        technical_data={
                            'volume_ratio': volume_ratio,
                            'price_change_pct': price_change_pct,
                            'avg_volume': avg_volume,
                            'is_bullish': is_bullish
                        }
                    )
                    
                    return signal
                    
        except Exception as e:
            log_warning(f"Error detecting volume surge: {e}")
        
        return None
    
    def detect_vwap_bounce(self, df: pd.DataFrame, market_data: MarketData) -> Optional[TradingSignal]:
        """Detect VWAP bounce signals"""
        if len(df) < 20:
            return None

        try:
            # Calculate VWAP
            vwap = self._calculate_vwap(df)
            if vwap is None:
                return None

            current_price = market_data.price
            price_distance_pct = abs(current_price - vwap) / vwap

            # Look for price near VWAP (within 0.5%)
            if price_distance_pct <= 0.005:
                # Check recent price action to determine bounce direction
                recent_prices = df['close'].tail(5)
                is_bouncing_up = recent_prices.iloc[-1] > recent_prices.iloc[-3]

                if is_bouncing_up:
                    target_price = current_price + config.trading.target_profit_dollars / 100
                    stop_loss_price = vwap - (config.trading.stop_loss_dollars / 100)
                else:
                    target_price = current_price - config.trading.target_profit_dollars / 100
                    stop_loss_price = vwap + (config.trading.stop_loss_dollars / 100)

                # Confidence based on proximity to VWAP and volume
                volume_ratio = market_data.volume / market_data.avg_volume if market_data.avg_volume > 0 else 1
                confidence = min(0.8, 0.4 + (1 - price_distance_pct) * 2 + (volume_ratio * 0.1))

                signal = TradingSignal(
                    symbol=market_data.symbol,
                    signal_type=SignalType.VWAP_BOUNCE,
                    timestamp=datetime.now(),
                    confidence=confidence,
                    entry_price=current_price,
                    target_price=target_price,
                    stop_loss_price=stop_loss_price,
                    expected_profit=config.trading.target_profit_dollars,
                    risk_reward_ratio=abs(target_price - current_price) / abs(current_price - stop_loss_price),
                    volume_ratio=volume_ratio,
                    technical_data={
                        'vwap': vwap,
                        'price_distance_pct': price_distance_pct,
                        'is_bouncing_up': is_bouncing_up,
                        'volume_ratio': volume_ratio
                    }
                )

                return signal

        except Exception as e:
            log_warning(f"Error detecting VWAP bounce: {e}")

        return None

    def detect_ttm_squeeze_breakout(self, df: pd.DataFrame, market_data: MarketData) -> Optional[TradingSignal]:
        """
        Detect TTM Squeeze pre-breakout compression patterns.

        Target Pattern:
        1. Compression Phase: 4-5 consecutive red histogram bars (decreasing negative momentum)
        2. Transition Phase: 2+ yellow histogram bars (rising but still negative)
        3. Entry Timing: Just before momentum turns green (positive)
        """
        if len(df) < 50:  # Need sufficient data for reliable squeeze detection
            return None

        try:
            # Calculate TTM Squeeze components
            squeeze_data = self._calculate_ttm_squeeze(df)
            if not squeeze_data:
                return None

            # Extract squeeze components
            bb_upper = squeeze_data['bb_upper']
            bb_lower = squeeze_data['bb_lower']
            kc_upper = squeeze_data['kc_upper']
            kc_lower = squeeze_data['kc_lower']
            momentum = squeeze_data['momentum']
            squeeze_on = squeeze_data['squeeze_on']

            # Detect pre-breakout compression pattern
            pattern_detected = self._detect_pre_breakout_pattern(momentum, squeeze_on)
            if not pattern_detected:
                return None

            # Get pattern details
            pattern_type = pattern_detected['type']
            pattern_confidence = pattern_detected['confidence']
            momentum_direction = pattern_detected['direction']

            # Calculate ATR-based position sizing and risk management
            atr = self._calculate_atr(df)
            if not atr:
                return None

            current_price = market_data.price

            # Calculate position size based on $10 fixed risk and 2x ATR stop
            stop_distance = 2.0 * atr
            fixed_risk = 10.0  # $10 fixed risk per trade
            position_size = fixed_risk / stop_distance

            # Ensure position size doesn't exceed account limits
            max_position_value = min(config.trading.max_position_size, market_data.price * position_size)
            position_size = max_position_value / market_data.price

            # Set entry, target, and stop prices based on momentum direction
            if momentum_direction == 'bullish':
                # Bullish breakout expected
                entry_price = current_price
                stop_loss_price = current_price - stop_distance

                # Dynamic target based on momentum strength and trailing stop
                base_target = current_price + (1.5 * atr)  # 1.5x ATR initial target
                max_target = current_price + (3.0 * atr)   # 3x ATR maximum target
                target_price = min(max_target, base_target * (1 + pattern_confidence))

                trailing_stop_distance = 1.5 * atr  # 1.5x ATR trailing stop

            else:
                # Bearish breakout expected
                entry_price = current_price
                stop_loss_price = current_price + stop_distance

                base_target = current_price - (1.5 * atr)
                max_target = current_price - (3.0 * atr)
                target_price = max(max_target, base_target * (1 + pattern_confidence))

                trailing_stop_distance = 1.5 * atr

            # Calculate expected profit and risk-reward ratio
            expected_profit = abs(target_price - entry_price) * position_size
            risk_amount = abs(entry_price - stop_loss_price) * position_size
            risk_reward_ratio = expected_profit / risk_amount if risk_amount > 0 else 0

            # Enhanced confidence calculation
            volume_ratio = market_data.volume / market_data.avg_volume if market_data.avg_volume > 0 else 1

            # Base confidence from pattern detection
            confidence = pattern_confidence

            # Boost confidence for high volume
            if volume_ratio > 1.5:
                confidence *= 1.2
            elif volume_ratio > 1.2:
                confidence *= 1.1

            # Boost confidence for strong momentum compression
            recent_momentum = momentum.tail(10)
            momentum_compression = 1.0 - (recent_momentum.std() / recent_momentum.abs().mean()) if recent_momentum.abs().mean() > 0 else 0
            confidence *= (1.0 + momentum_compression * 0.3)

            # Cap confidence at 0.95
            confidence = min(0.95, confidence)

            # Only generate signal if confidence is above threshold
            min_confidence = config.trading.ttm_squeeze_min_confidence
            if confidence < min_confidence:
                return None

            signal = TradingSignal(
                symbol=market_data.symbol,
                signal_type=SignalType.SQUEEZE_BREAKOUT,
                timestamp=datetime.now(),
                confidence=confidence,
                entry_price=entry_price,
                target_price=target_price,
                stop_loss_price=stop_loss_price,
                expected_profit=expected_profit,
                risk_reward_ratio=risk_reward_ratio,
                volume_ratio=volume_ratio,
                technical_data={
                    'atr': atr,
                    'position_size': position_size,
                    'stop_distance': stop_distance,
                    'trailing_stop_distance': trailing_stop_distance,
                    'momentum_direction': momentum_direction,
                    'pattern_type': pattern_type,
                    'pattern_confidence': pattern_confidence,
                    'momentum_compression': momentum_compression,
                    'squeeze_on': squeeze_on.iloc[-1] if len(squeeze_on) > 0 else False,
                    'current_momentum': momentum.iloc[-1] if len(momentum) > 0 else 0,
                    'volume_ratio': volume_ratio,
                    'risk_amount': risk_amount,
                    'is_bullish': momentum_direction == 'bullish'
                }
            )

            return signal

        except Exception as e:
            log_warning(f"Error detecting TTM Squeeze breakout: {e}")

        return None
    
    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> Optional[float]:
        """Calculate Average True Range"""
        try:
            atr = ta.volatility.AverageTrueRange(df['high'], df['low'], df['close'], window=period).average_true_range()
            return float(atr.iloc[-1]) if atr is not None and not pd.isna(atr.iloc[-1]) else None
        except:
            return None
    
    def _calculate_vwap(self, df: pd.DataFrame) -> Optional[float]:
        """Calculate Volume Weighted Average Price"""
        try:
            # VWAP = Sum(Price * Volume) / Sum(Volume)
            typical_price = (df['high'] + df['low'] + df['close']) / 3
            vwap = (typical_price * df['volume']).sum() / df['volume'].sum()
            return float(vwap)
        except:
            return None

    def _calculate_ttm_squeeze(self, df: pd.DataFrame, bb_period: int = None, bb_std: float = None,
                              kc_period: int = None, kc_atr_mult: float = None) -> Optional[dict]:
        """
        Calculate TTM Squeeze components:
        - Bollinger Bands vs Keltner Channels for squeeze detection
        - Linear regression momentum histogram
        """
        try:
            # Use config defaults if not provided
            if bb_period is None:
                bb_period = config.trading.ttm_squeeze_bb_period
            if bb_std is None:
                bb_std = config.trading.ttm_squeeze_bb_std
            if kc_period is None:
                kc_period = config.trading.ttm_squeeze_kc_period
            if kc_atr_mult is None:
                kc_atr_mult = config.trading.ttm_squeeze_kc_atr_mult
            # Calculate Bollinger Bands
            bb_indicator = ta.volatility.BollingerBands(df['close'], window=bb_period, window_dev=bb_std)
            bb_upper = bb_indicator.bollinger_hband()
            bb_lower = bb_indicator.bollinger_lband()
            bb_middle = bb_indicator.bollinger_mavg()

            # Calculate Keltner Channels
            kc_middle = ta.trend.EMAIndicator(df['close'], window=kc_period).ema_indicator()
            atr = ta.volatility.AverageTrueRange(df['high'], df['low'], df['close'], window=kc_period).average_true_range()
            kc_upper = kc_middle + (kc_atr_mult * atr)
            kc_lower = kc_middle - (kc_atr_mult * atr)

            # Squeeze detection: BB inside KC
            squeeze_on = (bb_upper <= kc_upper) & (bb_lower >= kc_lower)

            # Calculate momentum using linear regression
            momentum = self._calculate_momentum_histogram(df, period=bb_period)

            return {
                'bb_upper': bb_upper,
                'bb_lower': bb_lower,
                'bb_middle': bb_middle,
                'kc_upper': kc_upper,
                'kc_lower': kc_lower,
                'kc_middle': kc_middle,
                'squeeze_on': squeeze_on,
                'momentum': momentum,
                'atr': atr
            }

        except Exception as e:
            log_warning(f"Error calculating TTM Squeeze: {e}")
            return None

    def _calculate_momentum_histogram(self, df: pd.DataFrame, period: int = 20) -> pd.Series:
        """
        Calculate momentum histogram using linear regression - matches TTM Squeeze indicator.
        This creates the red/yellow/green histogram bars exactly like in your chart.
        """
        try:
            # Calculate the basis for momentum (Keltner Channel middle)
            kc_middle = df['close'].rolling(window=period).mean()

            # Calculate highest high and lowest low over the period
            highest = df['high'].rolling(window=period).max()
            lowest = df['low'].rolling(window=period).min()

            # Calculate the midpoint of the range
            midpoint = (highest + lowest) / 2

            # TTM Squeeze momentum calculation
            # This is the actual formula used in the TTM Squeeze indicator
            momentum = df['close'] - ((kc_middle + midpoint) / 2)

            # Apply smoothing to match the indicator behavior
            momentum = momentum.rolling(window=3).mean()

            return momentum

        except Exception as e:
            log_warning(f"Error calculating momentum histogram: {e}")
            return pd.Series(dtype=float)

    def _detect_pre_breakout_pattern(self, momentum: pd.Series, squeeze_on: pd.Series) -> Optional[dict]:
        """
        Detect the specific pre-breakout compression pattern based on the chart example:
        1. Extended Red Phase: Long series of red histogram bars (momentum compression)
        2. Yellow Transition: Yellow bars showing momentum rising but still negative
        3. Optimal Entry: Just before momentum crosses zero (the arrow point in chart)
        """
        try:
            if len(momentum) < 15:  # Need more history for this pattern
                return None

            # Get extended momentum history for better pattern detection
            recent_momentum = momentum.tail(15).dropna()
            recent_squeeze = squeeze_on.tail(15).dropna()

            if len(recent_momentum) < 12:
                return None

            # Check for recent squeeze activity (squeeze can be ending as we enter)
            recent_squeeze_activity = recent_squeeze.tail(5).sum() if len(recent_squeeze) > 0 else 0
            if recent_squeeze_activity < 2:  # Need some recent squeeze activity
                return None

            momentum_values = recent_momentum.values

            # Enhanced pattern detection based on chart analysis
            red_phase_count = 0
            yellow_phase_count = 0
            pattern_confidence = 0.0
            momentum_direction = None

            # Phase 1: Extended Red Compression Phase (like in your chart)
            # Look for a longer series of red bars showing sustained compression
            for i in range(len(momentum_values) - 8, len(momentum_values) - 3):
                if i >= 1 and momentum_values[i] < 0:
                    # Red bar: negative momentum that's either decreasing or slowly rising
                    if momentum_values[i] <= momentum_values[i-1] or \
                       (momentum_values[i] > momentum_values[i-1] and abs(momentum_values[i] - momentum_values[i-1]) < 0.1):
                        red_phase_count += 1

            # Phase 2: Yellow Transition Phase (momentum rising but negative)
            # Look for recent bars where momentum is clearly rising but still negative
            for i in range(len(momentum_values) - 4, len(momentum_values)):
                if i >= 1 and momentum_values[i] < 0 and momentum_values[i] > momentum_values[i-1]:
                    # Yellow bar: negative but rising momentum
                    rise_rate = momentum_values[i] - momentum_values[i-1]
                    if rise_rate > 0.05:  # Significant upward movement
                        yellow_phase_count += 1

            # Phase 3: Optimal Entry Detection (the arrow point)
            latest_momentum = momentum_values[-1]
            prev_momentum = momentum_values[-2]
            momentum_trend = latest_momentum - prev_momentum

            # Entry criteria based on chart pattern
            optimal_entry_detected = False

            # Check if we're at the optimal entry point (arrow in chart)
            if (latest_momentum < 0.2 and latest_momentum > -0.2 and  # Near zero (can be slightly positive)
                momentum_trend > 0 and  # Rising momentum
                yellow_phase_count >= 1 and  # Had yellow transition (more sensitive)
                red_phase_count >= 3):  # Had red compression (more sensitive)

                optimal_entry_detected = True

                # Determine direction based on momentum acceleration
                if momentum_trend > 0:
                    momentum_direction = 'bullish'
                    pattern_confidence = 0.65  # Good confidence for this setup
                elif latest_momentum > -0.05:  # Very close to zero
                    momentum_direction = 'bullish'
                    pattern_confidence = 0.60  # Minimum confidence

                # Boost confidence for stronger patterns
                if red_phase_count >= 6:  # Extended compression like in chart
                    pattern_confidence += 0.1

                if yellow_phase_count >= 3:  # Strong transition phase
                    pattern_confidence += 0.1

                # Check momentum acceleration (key for timing)
                if len(momentum_values) >= 3:
                    prev_trend = momentum_values[-2] - momentum_values[-3]
                    if momentum_trend > prev_trend:  # Accelerating upward
                        pattern_confidence += 0.1

                # Verify squeeze compression quality
                momentum_range = max(momentum_values[-8:]) - min(momentum_values[-8:])
                if momentum_range < 0.5:  # Tight compression
                    pattern_confidence += 0.05

                # Cap confidence
                pattern_confidence = min(0.95, pattern_confidence)

                # Determine pattern strength
                if red_phase_count >= 6 and yellow_phase_count >= 3:
                    pattern_type = 'textbook_squeeze'  # Like your chart example
                elif red_phase_count >= 5 and yellow_phase_count >= 2:
                    pattern_type = 'strong_compression'
                else:
                    pattern_type = 'moderate_compression'

                return {
                    'type': pattern_type,
                    'confidence': pattern_confidence,
                    'direction': momentum_direction,
                    'red_count': red_phase_count,
                    'yellow_count': yellow_phase_count,
                    'momentum_trend': momentum_trend,
                    'latest_momentum': latest_momentum,
                    'optimal_entry': optimal_entry_detected,
                    'momentum_acceleration': momentum_trend > prev_trend if len(momentum_values) >= 3 else False,
                    'compression_quality': momentum_range if 'momentum_range' in locals() else 0
                }

            return None

        except Exception as e:
            log_warning(f"Error detecting pre-breakout pattern: {e}")
            return None
    
    def analyze_stock(self, symbol: str, df: pd.DataFrame, market_data: MarketData) -> List[TradingSignal]:
        """Analyze a stock and return all detected signals"""
        signals = []
        
        try:
            # Run all signal detection methods
            signal_methods = [
                self.detect_momentum_breakout,
                self.detect_rsi_signals,
                self.detect_volume_surge,
                self.detect_vwap_bounce,
                self.detect_ttm_squeeze_breakout  # Add TTM Squeeze detection
            ]
            
            for method in signal_methods:
                try:
                    signal = method(df, market_data)
                    if signal and signal.confidence >= 0.3:  # Minimum confidence threshold
                        signals.append(signal)
                        log_debug(f"Signal detected: {symbol} - {signal.signal_type.value} (confidence: {signal.confidence:.2f})")
                except Exception as e:
                    log_warning(f"Error in signal method {method.__name__} for {symbol}: {e}")
            
            # Sort signals by confidence
            signals.sort(key=lambda x: x.confidence, reverse=True)
            
        except Exception as e:
            log_warning(f"Error analyzing stock {symbol}: {e}")
        
        return signals

# Global technical analyzer instance
analyzer = TechnicalAnalyzer()

if __name__ == "__main__":
    # Test technical analysis
    import yfinance as yf
    
    # Get sample data
    ticker = yf.Ticker("AAPL")
    df = ticker.history(period="1d", interval="1m")
    
    if not df.empty:
        # Rename columns to match our format
        df.columns = [col.lower() for col in df.columns]
        
        # Create sample market data
        market_data = MarketData(
            symbol="AAPL",
            timestamp=datetime.now(),
            price=df['close'].iloc[-1],
            volume=int(df['volume'].iloc[-1]),
            open_price=df['open'].iloc[-1],
            high=df['high'].iloc[-1],
            low=df['low'].iloc[-1],
            close=df['close'].iloc[-1],
            avg_volume=int(df['volume'].mean())
        )
        
        # Analyze stock
        signals = analyzer.analyze_stock("AAPL", df, market_data)
        print(f"Found {len(signals)} signals for AAPL")
        
        for signal in signals:
            print(f"Signal: {signal.signal_type.value} - Confidence: {signal.confidence:.2%}")
    else:
        print("No data available for testing")
