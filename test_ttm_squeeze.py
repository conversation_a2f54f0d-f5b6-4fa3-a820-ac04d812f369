"""
Test script for TTM Squeeze momentum detection.
Validates the pre-breakout compression pattern recognition.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

from technical_analysis import analyzer
from models import MarketData
from config import config

def create_test_data_with_squeeze_pattern():
    """Create synthetic data with a TTM Squeeze pattern"""
    
    # Create 100 bars of data
    dates = pd.date_range(start='2024-01-01', periods=100, freq='1min')
    
    # Base price around $50
    base_price = 50.0
    
    # Create a squeeze pattern
    prices = []
    volumes = []
    
    for i in range(100):
        if i < 30:
            # Normal volatility phase
            price_change = np.random.normal(0, 0.5)
            volume = np.random.randint(80000, 120000)
        elif i < 60:
            # Compression phase - decreasing volatility
            volatility = 0.5 * (1 - (i - 30) / 30)  # Decreasing volatility
            price_change = np.random.normal(0, volatility)
            volume = np.random.randint(60000, 100000)
        elif i < 80:
            # Tight squeeze - very low volatility
            price_change = np.random.normal(0, 0.1)
            volume = np.random.randint(50000, 80000)
        else:
            # Breakout phase - increasing volatility and trend
            trend = 0.2 * (i - 80)  # Upward trend
            volatility = 0.3 + 0.1 * (i - 80)  # Increasing volatility
            price_change = trend + np.random.normal(0, volatility)
            volume = np.random.randint(120000, 200000)  # Higher volume
        
        if i == 0:
            prices.append(base_price)
        else:
            prices.append(prices[-1] + price_change)
        
        volumes.append(volume)
    
    # Create OHLC data
    df = pd.DataFrame({
        'datetime': dates,
        'open': prices,
        'high': [p + abs(np.random.normal(0, 0.1)) for p in prices],
        'low': [p - abs(np.random.normal(0, 0.1)) for p in prices],
        'close': prices,
        'volume': volumes
    })
    
    # Ensure high >= close >= low and high >= open >= low
    for i in range(len(df)):
        df.loc[i, 'high'] = max(df.loc[i, 'open'], df.loc[i, 'close'], df.loc[i, 'high'])
        df.loc[i, 'low'] = min(df.loc[i, 'open'], df.loc[i, 'close'], df.loc[i, 'low'])
    
    return df

def test_ttm_squeeze_detection():
    """Test TTM Squeeze detection on synthetic data"""
    print("🧪 Testing TTM Squeeze Detection")
    print("=" * 50)
    
    # Create test data
    df = create_test_data_with_squeeze_pattern()
    
    # Create market data for the latest bar
    latest_bar = df.iloc[-1]
    market_data = MarketData(
        symbol="TEST",
        timestamp=datetime.now(),
        price=latest_bar['close'],
        volume=int(latest_bar['volume']),
        open_price=latest_bar['open'],
        high=latest_bar['high'],
        low=latest_bar['low'],
        close=latest_bar['close'],
        avg_volume=int(df['volume'].mean())
    )
    
    print(f"📊 Test Data Created:")
    print(f"   Bars: {len(df)}")
    print(f"   Price Range: ${df['close'].min():.2f} - ${df['close'].max():.2f}")
    print(f"   Current Price: ${market_data.price:.2f}")
    print(f"   Average Volume: {market_data.avg_volume:,}")
    
    # Test TTM Squeeze calculation
    print(f"\n🔍 Testing TTM Squeeze Components:")
    squeeze_data = analyzer._calculate_ttm_squeeze(df)
    
    if squeeze_data:
        print(f"   ✅ TTM Squeeze calculation successful")
        print(f"   Squeeze Active: {squeeze_data['squeeze_on'].iloc[-1]}")
        print(f"   Current Momentum: {squeeze_data['momentum'].iloc[-1]:.4f}")
        print(f"   Recent Squeeze Count: {squeeze_data['squeeze_on'].tail(10).sum()}")
    else:
        print(f"   ❌ TTM Squeeze calculation failed")
        return
    
    # Test pattern detection
    print(f"\n🎯 Testing Pre-Breakout Pattern Detection:")
    pattern = analyzer._detect_pre_breakout_pattern(
        squeeze_data['momentum'], 
        squeeze_data['squeeze_on']
    )
    
    if pattern:
        print(f"   ✅ Pattern detected!")
        print(f"   Pattern Type: {pattern['type']}")
        print(f"   Confidence: {pattern['confidence']:.2%}")
        print(f"   Direction: {pattern['direction']}")
        print(f"   Red Bars: {pattern['red_count']}")
        print(f"   Yellow Bars: {pattern['yellow_count']}")
        print(f"   Momentum Trend: {pattern['momentum_trend']:.4f}")
    else:
        print(f"   ℹ️ No pre-breakout pattern detected")
    
    # Test full signal generation
    print(f"\n🚀 Testing Full Signal Generation:")
    signal = analyzer.detect_ttm_squeeze_breakout(df, market_data)
    
    if signal:
        print(f"   ✅ TTM Squeeze signal generated!")
        print(f"   Symbol: {signal.symbol}")
        print(f"   Signal Type: {signal.signal_type.value}")
        print(f"   Confidence: {signal.confidence:.2%}")
        print(f"   Entry Price: ${signal.entry_price:.2f}")
        print(f"   Target Price: ${signal.target_price:.2f}")
        print(f"   Stop Loss: ${signal.stop_loss_price:.2f}")
        print(f"   Expected Profit: ${signal.expected_profit:.2f}")
        print(f"   Risk/Reward: {signal.risk_reward_ratio:.2f}")
        
        if signal.technical_data:
            tech_data = signal.technical_data
            print(f"   ATR: ${tech_data.get('atr', 0):.4f}")
            print(f"   Position Size: {tech_data.get('position_size', 0):.2f} shares")
            print(f"   Direction: {tech_data.get('momentum_direction', 'unknown')}")
            print(f"   Pattern: {tech_data.get('pattern_type', 'unknown')}")
    else:
        print(f"   ℹ️ No TTM Squeeze signal generated")
    
    return df, squeeze_data, signal

def plot_ttm_squeeze_analysis(df, squeeze_data, signal=None):
    """Plot TTM Squeeze analysis"""
    if not squeeze_data:
        print("No squeeze data to plot")
        return
    
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 10))
    
    # Plot 1: Price with Bollinger Bands and Keltner Channels
    ax1.plot(df.index, df['close'], label='Close Price', color='black', linewidth=2)
    ax1.plot(df.index, squeeze_data['bb_upper'], label='BB Upper', color='blue', alpha=0.7)
    ax1.plot(df.index, squeeze_data['bb_lower'], label='BB Lower', color='blue', alpha=0.7)
    ax1.plot(df.index, squeeze_data['kc_upper'], label='KC Upper', color='red', alpha=0.7)
    ax1.plot(df.index, squeeze_data['kc_lower'], label='KC Lower', color='red', alpha=0.7)
    ax1.fill_between(df.index, squeeze_data['bb_upper'], squeeze_data['bb_lower'], 
                     alpha=0.1, color='blue', label='BB Range')
    ax1.fill_between(df.index, squeeze_data['kc_upper'], squeeze_data['kc_lower'], 
                     alpha=0.1, color='red', label='KC Range')
    
    if signal:
        ax1.axvline(x=len(df)-1, color='green', linestyle='--', linewidth=2, label='Signal')
        ax1.plot(len(df)-1, signal.entry_price, 'go', markersize=10, label='Entry')
    
    ax1.set_title('TTM Squeeze - Price with Bands')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: Squeeze Indicator
    squeeze_colors = ['red' if x else 'green' for x in squeeze_data['squeeze_on']]
    ax2.scatter(df.index, [1]*len(df), c=squeeze_colors, s=50, alpha=0.7)
    ax2.set_ylim(0.5, 1.5)
    ax2.set_title('Squeeze Indicator (Red = Squeeze On, Green = Squeeze Off)')
    ax2.set_ylabel('Squeeze Status')
    ax2.grid(True, alpha=0.3)
    
    # Plot 3: Momentum Histogram
    momentum = squeeze_data['momentum'].dropna()
    colors = []
    for i, val in enumerate(momentum):
        if val < 0:
            if i > 0 and val > momentum.iloc[i-1]:
                colors.append('orange')  # Rising negative (yellow phase)
            else:
                colors.append('red')     # Falling negative (red phase)
        else:
            colors.append('green')       # Positive (green phase)
    
    ax3.bar(momentum.index, momentum.values, color=colors, alpha=0.7)
    ax3.axhline(y=0, color='black', linestyle='-', linewidth=1)
    ax3.set_title('Momentum Histogram (Red/Orange/Green)')
    ax3.set_ylabel('Momentum')
    ax3.grid(True, alpha=0.3)
    
    if signal:
        ax3.axvline(x=len(df)-1, color='blue', linestyle='--', linewidth=2, label='Signal')
    
    plt.tight_layout()
    plt.show()

def run_comprehensive_test():
    """Run comprehensive TTM Squeeze tests"""
    print("🚀 COMPREHENSIVE TTM SQUEEZE TEST")
    print("=" * 60)
    
    # Test configuration
    print(f"\n⚙️ Configuration:")
    print(f"   TTM Squeeze Enabled: {config.trading.ttm_squeeze_enabled}")
    print(f"   BB Period: {config.trading.ttm_squeeze_bb_period}")
    print(f"   KC Period: {config.trading.ttm_squeeze_kc_period}")
    print(f"   Min Compression Bars: {config.trading.ttm_squeeze_min_compression_bars}")
    print(f"   Min Transition Bars: {config.trading.ttm_squeeze_min_transition_bars}")
    print(f"   Min Confidence: {config.trading.ttm_squeeze_min_confidence:.0%}")
    
    # Run test
    df, squeeze_data, signal = test_ttm_squeeze_detection()
    
    # Plot results
    print(f"\n📊 Generating plots...")
    plot_ttm_squeeze_analysis(df, squeeze_data, signal)
    
    print(f"\n✅ Test completed successfully!")
    
    return df, squeeze_data, signal

if __name__ == "__main__":
    # Run the comprehensive test
    test_results = run_comprehensive_test()
